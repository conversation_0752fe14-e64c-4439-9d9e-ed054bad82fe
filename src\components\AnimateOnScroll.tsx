import type { ReactNode } from "react";
import { useIntersectionObserver } from "../hooks/use-intersection-observer";

interface AnimateOnScrollProps {
  children: ReactNode;
  className?: string;
  threshold?: number;
  rootMargin?: string;
}

export const AnimateOnScroll = ({
  children,
  className = "",
  threshold = 0.1,
  rootMargin = "0px",
}: AnimateOnScrollProps) => {
  const [ref, isInView] = useIntersectionObserver({
    threshold,
    rootMargin,
  });

  return (
    <div
      ref={ref as React.RefObject<HTMLDivElement>}
      className={`${className} ${isInView ? "in-view" : ""}`}
    >
      {children}
    </div>
  );
};
