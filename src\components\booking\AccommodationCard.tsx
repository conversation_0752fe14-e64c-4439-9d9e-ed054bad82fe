import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Users, Bed, Wifi, Coffee, Shield, Check } from "lucide-react";
import type { Accommodation } from "@/types/booking";

interface AccommodationCardProps {
  accommodation: Accommodation;
  isSelected?: boolean;
  onSelect?: (accommodation: Accommodation) => void;
  showPrice?: boolean;
  compact?: boolean;
}

export function AccommodationCard({
  accommodation,
  isSelected = false,
  onSelect,
  showPrice = true,
  compact = false,
}: AccommodationCardProps) {
  const getIconForAmenity = (amenity: string) => {
    if (amenity.toLowerCase().includes("wifi"))
      return <Wifi className="h-4 w-4" />;
    if (
      amenity.toLowerCase().includes("coffee") ||
      amenity.toLowerCase().includes("tea")
    )
      return <Coffee className="h-4 w-4" />;
    if (amenity.toLowerCase().includes("safe"))
      return <Shield className="h-4 w-4" />;
    return <Check className="h-4 w-4" />;
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "luxury":
        return "bg-[#DAA520]/20 text-[#DAA520] border-[#DAA520]";
      case "premium":
        return "bg-[#8B4513]/20 text-[#8B4513] border-[#8B4513]";
      default:
        return "bg-[#2C5530]/20 text-[#2C5530] border-[#2C5530]";
    }
  };

  if (compact) {
    return (
      <Card
        className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
          isSelected
            ? "border-[#2C5530] bg-[#2C5530]/5 shadow-lg"
            : "border-neutral-200 hover:border-[#DAA520]"
        }`}
        onClick={() => onSelect?.(accommodation)}
      >
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
              <img
                src={accommodation.image}
                alt={accommodation.title}
                className="w-full h-full object-cover"
              />
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold text-[#2C5530] truncate">
                  {accommodation.title}
                </h3>
                {isSelected && (
                  <Check className="h-5 w-5 text-[#2C5530] flex-shrink-0" />
                )}
              </div>

              <div className="flex flex-col md:flex-row md:items-center gap-4 text-sm text-neutral-600 mb-2">
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  <span>{accommodation.maxGuests}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Bed className="h-4 w-4" />
                  <span className="truncate">
                    {accommodation.bedConfiguration}
                  </span>
                </div>
              </div>

              {showPrice && (
                <div className="text-right">
                  <div className="text-lg font-bold text-[#8B4513]">
                    ${accommodation.pricePerNight}
                  </div>
                  <div className="text-xs text-neutral-500">per night</div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className={`overflow-hidden group transition-all duration-500 cursor-pointer ${
        isSelected
          ? "border-[#2C5530] bg-[#2C5530]/5 shadow-xl"
          : "border-neutral-200 hover:shadow-xl hover:border-[#DAA520]"
      }`}
      onClick={() => onSelect?.(accommodation)}
    >
      {/* Image */}
      <AspectRatio ratio={16 / 10}>
        <img
          src={accommodation.image}
          alt={accommodation.title}
          className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-105"
        />
        {isSelected && (
          <div className="absolute top-4 right-4 bg-[#2C5530] text-white p-2 rounded-full">
            <Check className="h-5 w-5" />
          </div>
        )}
      </AspectRatio>

      <CardHeader className="pb-4">
        <div className="flex items-center justify-between mb-2">
          <Badge
            variant="outline"
            className={`${getCategoryColor(
              accommodation.category
            )} capitalize font-semibold`}
          >
            {accommodation.category}
          </Badge>
          {showPrice && (
            <div className="text-right">
              <div className="text-2xl font-bold text-[#8B4513]">
                ${accommodation.pricePerNight}
              </div>
              <div className="text-sm text-neutral-600">per night</div>
            </div>
          )}
        </div>

        <CardTitle className="playfair-display-heading text-xl font-bold text-[#2C5530] mb-2">
          {accommodation.title}
        </CardTitle>

        <p className="text-neutral-600 text-sm leading-relaxed">
          {accommodation.description}
        </p>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Key Details */}
        <div className="grid grid-cols-2 gap-3 mb-4 p-3 bg-neutral-50 rounded-lg">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-[#2C5530]" />
            <span className="text-sm">
              Up to {accommodation.maxGuests} guests
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Bed className="h-4 w-4 text-[#2C5530]" />
            <span className="text-sm">{accommodation.bedConfiguration}</span>
          </div>
          <div className="col-span-2 text-sm text-neutral-600">
            <strong>Size:</strong> {accommodation.size}
          </div>
        </div>

        {/* Top Amenities */}
        <div className="mb-4">
          <h4 className="font-semibold text-[#2C5530] mb-2 text-sm">
            Key Amenities
          </h4>
          <div className="grid grid-cols-1 gap-1">
            {accommodation.amenities.slice(0, 4).map((amenity, idx) => (
              <div
                key={idx}
                className="flex items-center gap-2 text-sm text-neutral-600"
              >
                {getIconForAmenity(amenity)}
                <span className="truncate">{amenity}</span>
              </div>
            ))}
            {accommodation.amenities.length > 4 && (
              <div className="text-sm text-[#8B4513] font-medium">
                +{accommodation.amenities.length - 4} more amenities
              </div>
            )}
          </div>
        </div>

        {/* Select Button */}
        {onSelect && (
          <Button
            className={`w-full transition-all duration-300 ${
              isSelected
                ? "bg-[#2C5530] hover:bg-[#8B4513] text-white"
                : "bg-gradient-to-r from-[#2C5530] to-[#8B4513] hover:from-[#8B4513] hover:to-[#2C5530] text-white"
            }`}
          >
            {isSelected ? "Selected" : "Select This Accommodation"}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
