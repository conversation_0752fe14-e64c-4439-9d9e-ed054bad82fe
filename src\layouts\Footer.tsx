import {
  Facebook,
  Instagram,
  Mail,
  MapPin,
  Phone,
  Twitter,
} from "lucide-react";

export function Footer() {
  return (
    <footer className="bg-neutral-900 text-neutral-200">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Contact Information */}
          <div>
            <h3 className="text-xl font-semibold mb-4 text-amber-500">
              Contact Us
            </h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <MapPin className="h-5 w-5 text-amber-500" />
                <span>Nyerere National Park, Tanzania</span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-amber-500" />
                <span>+255 123 456 789</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-amber-500" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-xl font-semibold mb-4 text-amber-500">
              Quick Links
            </h3>
            <ul className="space-y-2">
              <li>
                <a
                  href="/accommodation"
                  className="hover:text-amber-500 transition-colors"
                >
                  Accommodation
                </a>
              </li>
              <li>
                <a
                  href="/activities"
                  className="hover:text-amber-500 transition-colors"
                >
                  Safari Activities
                </a>
              </li>
              <li>
                <a
                  href="/facilities"
                  className="hover:text-amber-500 transition-colors"
                >
                  Facilities
                </a>
              </li>
              <li>
                <a
                  href="/gallery"
                  className="hover:text-amber-500 transition-colors"
                >
                  Photo Gallery
                </a>
              </li>
            </ul>
          </div>

          {/* Social Media & Newsletter */}
          <div>
            <h3 className="text-xl font-semibold mb-4 text-amber-500">
              Connect With Us
            </h3>
            <div className="flex space-x-4 mb-6 justify-center">
              <a
                href="#"
                className="hover:text-amber-500 transition-colors"
                aria-label="Facebook"
              >
                <Facebook className="h-6 w-6" />
              </a>
              <a
                href="#"
                className="hover:text-amber-500 transition-colors"
                aria-label="Instagram"
              >
                <Instagram className="h-6 w-6" />
              </a>
              <a
                href="#"
                className="hover:text-amber-500 transition-colors"
                aria-label="Twitter"
              >
                <Twitter className="h-6 w-6" />
              </a>
            </div>
            <p className="text-sm text-neutral-400">
              Experience the untamed beauty of Nyerere National Park at Malombo
              Selous Forest Camp
            </p>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t border-neutral-800 text-center text-sm text-neutral-400">
          <p>
            © {new Date().getFullYear()} Malombo Selous Forest Camp. All rights
            reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
