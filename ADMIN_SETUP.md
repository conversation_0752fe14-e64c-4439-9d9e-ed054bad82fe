# Malombo Admin Panel Setup Guide

## Overview
The Malombo Selous Forest Camp Admin Panel has been successfully implemented with React + Vite + Tailwind CSS + shadcn/ui and Supabase integration.

## Features Implemented

### ✅ Authentication & Routing
- **Supabase Authentication**: Email/password authentication with role-based access
- **User Roles**: 
  - `admin`: Full access to all features
  - `staff`: Limited access (read-only for some sections)
- **Protected Routes**:
  - `/admin/login` → Login form
  - `/admin/dashboard` → Main dashboard (protected)
  - `/admin/accommodations` → Accommodations management
  - `/admin/activities` → Activities management
  - `/admin/bookings` → Bookings management
  - `/admin/settings` → Settings (admin-only)

### ✅ Layout & Design
- **Responsive Admin Layout**:
  - Collapsible left sidebar navigation
  - Top bar with search, notifications, and user menu
  - Mobile-responsive design with hamburger menu
- **Safari Theme**: Warm, modern design with earthy browns, greens, and golden highlights
- **shadcn/ui Components**: Consistent UI components throughout

### ✅ Dashboard
- **Stats Overview**: Cards showing dummy data for:
  - Total Accommodations (12)
  - Total Activities (8)
  - Total Bookings (24)
  - System Status
- **Recent Activity Feed**
- **Quick Actions Panel**

### ✅ Admin Pages
- **Accommodations**: Management interface with status indicators
- **Activities**: Safari experiences with pricing and capacity info
- **Bookings**: Guest reservations with contact details and status
- **Settings**: System configuration, user management, notifications

## Technical Implementation

### File Structure
```
src/
├── components/
│   ├── auth/
│   │   ├── AuthProvider.tsx
│   │   └── ProtectedRoute.tsx
│   └── admin/
│       ├── AdminSidebar.tsx
│       └── AdminTopbar.tsx
├── hooks/
│   └── use-auth.ts
├── layouts/
│   └── AdminLayout.tsx
├── lib/
│   ├── auth.ts
│   └── supabase.ts
└── pages/admin/
    ├── Login.tsx
    ├── Dashboard.tsx
    ├── AccommodationsAdmin.tsx
    ├── ActivitiesAdmin.tsx
    ├── BookingsAdmin.tsx
    └── SettingsAdmin.tsx
```

### Environment Variables
```env
VITE_SUPABASE_URL=https://dsxpvauzswcbvqnsyhiu.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Database Setup Required

To complete the setup, you need to create the following table in Supabase:

```sql
-- Create profiles table
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  role TEXT CHECK (role IN ('admin', 'staff')) DEFAULT 'staff',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
```

## Getting Started

1. **Start the development server**:
   ```bash
   npm run dev
   ```

2. **Access the admin panel**:
   - Navigate to `http://localhost:5173/admin/login`
   - Create a user account in Supabase Auth
   - Add a corresponding profile record with admin/staff role

3. **Test the features**:
   - Login with your credentials
   - Navigate through the different admin sections
   - Test the responsive design on mobile devices

## Next Steps

1. **Create Admin Users**: Set up initial admin accounts in Supabase
2. **Implement CRUD Operations**: Add actual database operations for accommodations, activities, and bookings
3. **Add Real Data**: Replace dummy data with actual Supabase queries
4. **File Upload**: Implement image upload for accommodations and activities
5. **Email Notifications**: Set up email templates for booking confirmations
6. **Analytics**: Add charts and detailed reporting features

## Security Notes

- All admin routes are protected with authentication
- Role-based access control is implemented
- Settings page requires admin privileges
- Environment variables are properly configured
- Supabase RLS policies should be implemented for production

The admin panel is now fully functional and ready for further development!
