-- =====================================================
-- Malombo Admin Panel - Row Level Security Policies
-- =====================================================
-- This file sets up RLS policies for the profiles table.
-- These policies are PERMISSIVE for development purposes.
-- 
-- ⚠️  WARNING: These policies are NOT production-ready!
-- ⚠️  Tighten security before deploying to production.
-- 
-- Safe to run multiple times (idempotent)
-- =====================================================

-- Enable Row Level Security on profiles table
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist (for idempotency)
DROP POLICY IF EXISTS "dev_profiles_select_all" ON public.profiles;
DROP POLICY IF EXISTS "dev_profiles_insert_all" ON public.profiles;
DROP POLICY IF EXISTS "dev_profiles_update_all" ON public.profiles;
DROP POLICY IF EXISTS "dev_profiles_delete_all" ON public.profiles;

-- =====================================================
-- DEVELOPMENT POLICIES (PERMISSIVE)
-- =====================================================

-- Policy 1: Allow authenticated users to SELECT all profiles
-- In production: Restrict to own profile or admin-only
CREATE POLICY "dev_profiles_select_all" 
ON public.profiles 
FOR SELECT 
TO authenticated 
USING (true);

-- Policy 2: Allow authenticated users to INSERT profiles
-- In production: Restrict to admin-only or specific conditions
CREATE POLICY "dev_profiles_insert_all" 
ON public.profiles 
FOR INSERT 
TO authenticated 
WITH CHECK (true);

-- Policy 3: Allow authenticated users to UPDATE any profile
-- In production: Restrict to own profile or admin-only
CREATE POLICY "dev_profiles_update_all" 
ON public.profiles 
FOR UPDATE 
TO authenticated 
USING (true)
WITH CHECK (true);

-- Policy 4: Allow authenticated users to DELETE profiles
-- In production: Restrict to admin-only
CREATE POLICY "dev_profiles_delete_all" 
ON public.profiles 
FOR DELETE 
TO authenticated 
USING (true);

-- =====================================================
-- PRODUCTION-READY POLICIES (COMMENTED OUT)
-- =====================================================
-- Uncomment and modify these for production deployment:

/*
-- Production Policy 1: Users can only view their own profile + admins can view all
CREATE POLICY "prod_profiles_select" 
ON public.profiles 
FOR SELECT 
TO authenticated 
USING (
    auth.uid() = id OR 
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- Production Policy 2: Only admins can insert new profiles
CREATE POLICY "prod_profiles_insert" 
ON public.profiles 
FOR INSERT 
TO authenticated 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- Production Policy 3: Users can update own profile + admins can update any
CREATE POLICY "prod_profiles_update" 
ON public.profiles 
FOR UPDATE 
TO authenticated 
USING (
    auth.uid() = id OR 
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    )
)
WITH CHECK (
    auth.uid() = id OR 
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    )
);

-- Production Policy 4: Only admins can delete profiles
CREATE POLICY "prod_profiles_delete" 
ON public.profiles 
FOR DELETE 
TO authenticated 
USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE id = auth.uid() AND role = 'admin'
    )
);
*/

-- Add helpful comments
COMMENT ON POLICY "dev_profiles_select_all" ON public.profiles IS 'DEV ONLY: Allow all authenticated users to read all profiles';
COMMENT ON POLICY "dev_profiles_insert_all" ON public.profiles IS 'DEV ONLY: Allow all authenticated users to insert profiles';
COMMENT ON POLICY "dev_profiles_update_all" ON public.profiles IS 'DEV ONLY: Allow all authenticated users to update any profile';
COMMENT ON POLICY "dev_profiles_delete_all" ON public.profiles IS 'DEV ONLY: Allow all authenticated users to delete profiles';

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'RLS policies created successfully - DEVELOPMENT MODE (permissive)';
    RAISE NOTICE 'WARNING: These policies are NOT production-ready!';
END $$;
