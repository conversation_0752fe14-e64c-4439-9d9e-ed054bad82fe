import type { Activity } from "@/types/booking";

export const activities: Activity[] = [
  {
    id: "game-drive",
    name: "Game Drive",
    description:
      "Explore the wilderness with our expert guides in custom safari vehicles. Experience close encounters with the Big Five and other wildlife in their natural habitat.",
    price: 80,
    duration: "3-4 hours",
    included: false,
    category: "safari",
    maxParticipants: 6,
    ageRestriction: "All ages welcome",
  },
  {
    id: "boat-safari",
    name: "Boat Safari",
    description:
      "Cruise along the mighty Rufiji River observing wildlife up close. Perfect for spotting hippos, crocodiles, and diverse bird species along the riverbanks.",
    price: 120,
    duration: "2-3 hours",
    included: false,
    category: "safari",
    maxParticipants: 8,
    ageRestriction: "Children must be 5+ years",
  },
  {
    id: "walking-safari",
    name: "Walking Safari",
    description:
      "Experience the bush on foot with our experienced rangers. Learn about animal tracks, medicinal plants, and the smaller wonders of the ecosystem.",
    price: 60,
    duration: "2-3 hours",
    included: false,
    category: "safari",
    maxParticipants: 8,
    ageRestriction: "Minimum age 12 years",
  },
  {
    id: "cultural-village-visit",
    name: "Cultural Village Visit",
    description:
      "Visit the nearby Mloka village and experience authentic Tanzanian culture. Meet local families, learn traditional crafts, and enjoy cultural performances.",
    price: 40,
    duration: "3-4 hours",
    included: false,
    category: "cultural",
    maxParticipants: 12,
    ageRestriction: "All ages welcome",
  },
  {
    id: "night-safari",
    name: "Night Safari",
    description:
      "Explore the wilderness after dark and discover the nocturnal wildlife. Use spotlights to observe animals that are active during the night.",
    price: 100,
    duration: "2-3 hours",
    included: false,
    category: "safari",
    maxParticipants: 6,
    ageRestriction: "Minimum age 8 years",
  },
  {
    id: "bird-watching",
    name: "Bird Watching Tour",
    description:
      "Discover over 400 bird species in Nyerere National Park with our expert ornithologist guides. Perfect for photography enthusiasts.",
    price: 70,
    duration: "3-4 hours",
    included: false,
    category: "safari",
    maxParticipants: 8,
    ageRestriction: "All ages welcome",
  },
  {
    id: "fishing-expedition",
    name: "Fishing Expedition",
    description:
      "Try your hand at fishing in the Rufiji River. All equipment provided, and you can enjoy your catch prepared by our chefs.",
    price: 90,
    duration: "4-5 hours",
    included: false,
    category: "adventure",
    maxParticipants: 4,
    ageRestriction: "Minimum age 10 years",
  },
  {
    id: "photography-workshop",
    name: "Wildlife Photography Workshop",
    description:
      "Learn wildlife photography techniques from professional photographers. Includes equipment rental and personalized guidance.",
    price: 150,
    duration: "Full day",
    included: false,
    category: "adventure",
    maxParticipants: 6,
    ageRestriction: "Minimum age 16 years",
  },
  {
    id: "spa-treatment",
    name: "Bush Spa Treatment",
    description:
      "Relax with traditional African spa treatments using natural ingredients. Enjoy massages and wellness treatments in our outdoor spa pavilion.",
    price: 80,
    duration: "1-2 hours",
    included: false,
    category: "relaxation",
    maxParticipants: 2,
    ageRestriction: "Adults only (18+)",
  },
  {
    id: "sunset-cocktails",
    name: "Sunset Cocktails",
    description:
      "Enjoy sundowner cocktails at a scenic viewpoint while watching the African sunset. Includes premium drinks and canapés.",
    price: 45,
    duration: "1-2 hours",
    included: false,
    category: "relaxation",
    maxParticipants: 20,
    ageRestriction: "Adults only (18+) for alcohol",
  },
];
