export interface Accommodation {
  id: string;
  title: string;
  description: string;
  detailedDescription: string;
  image: string;
  pricePerNight: number;
  maxGuests: number;
  amenities: string[];
  features: string[];
  category: 'luxury' | 'standard' | 'premium';
  size: string;
  bedConfiguration: string;
}

export interface Activity {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: string;
  included: boolean;
  category: 'safari' | 'cultural' | 'adventure' | 'relaxation';
  maxParticipants?: number;
  ageRestriction?: string;
}

export interface GuestInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  country: string;
  specialRequests?: string;
  dietaryRequirements?: string;
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
}

export interface BookingData {
  accommodation: Accommodation;
  checkIn: Date;
  checkOut: Date;
  guests: {
    adults: number;
    children: number;
  };
  activities: Activity[];
  guestInfo: GuestInfo;
  totalPrice: number;
  nights: number;
  accommodationTotal: number;
  activitiesTotal: number;
  bookingReference?: string;
}

export interface BookingStep {
  id: number;
  title: string;
  description: string;
  isComplete: boolean;
  isActive: boolean;
}

export interface PricingBreakdown {
  accommodationPrice: number;
  nights: number;
  accommodationTotal: number;
  activitiesTotal: number;
  subtotal: number;
  taxes: number;
  total: number;
}

export interface Season {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  priceMultiplier: number;
}
