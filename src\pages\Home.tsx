import { useNavigate } from "react-router-dom";
import { usePageTitle } from "@/hooks/use-page-title";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

const Home = () => {
  const navigate = useNavigate();
  usePageTitle("Home");

  return (
    <div className="flex flex-col w-full min-h-screen">
      {/* Hero Section */}
      <section id="home" className="relative w-full h-screen">
        <div className="absolute inset-0">
          {/* Placeholder safari image - replace with actual image */}
          <img
            src="/images/hero-safari.jpg"
            alt="Malombo Selous Forest Camp"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black/50" /> {/* Dark overlay */}
        </div>
        <div className="relative z-10 container mx-auto h-full flex flex-col items-center justify-center text-center text-white px-4">
          <h1 className="text-5xl md:text-6xl mb-4 playfair-display-heading">
            <span className="text-[#DAA520]">Malombo</span>{" "}
            <span className="text-white">Selous Forest Camp</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-2xl playfair-display-subheading text-[#FFA07A]">
            More of a home away from home than just a safari camp
          </p>
          <Button
            size="lg"
            className="bg-gradient-to-r from-[#DAA520] to-[#8B4513] hover:from-[#8B4513] hover:to-[#DAA520] text-white transition-all duration-300"
          >
            Book Your Safari
          </Button>
        </div>
      </section>

      {/* Highlights Section */}
      <section className="py-16 bg-muted w-[90%] mx-auto">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Location Card */}
            <Card>
              <CardHeader>
                <CardTitle className="playfair-display-subheading">
                  Location
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p>
                  500m from Mloka village, 6.8km from Mtemere Gate, inside
                  Nyerere National Park (formerly Selous)
                </p>
              </CardContent>
            </Card>

            {/* Wildlife Card */}
            <Card>
              <CardHeader>
                <CardTitle className="playfair-display-subheading">
                  Wildlife
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p>
                  Big Five, endangered wild dogs, hippos, buffaloes, and 2000+
                  bird species
                </p>
              </CardContent>
            </Card>

            {/* Eco-Friendly Card */}
            <Card>
              <CardHeader>
                <CardTitle className="playfair-display-subheading">
                  Eco-Friendly Lodge
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p>
                  Combines modern comfort with nature, using sustainable water
                  and power solutions
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Welcome Section */}
      <section
        id="about"
        className="py-16 w-[90%] mx-auto"
        style={{ animationDelay: "200ms" }}
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl text-center mb-8 playfair-display-heading text-[#2C5530]  hover:text-gradient transition-all duration-300">
            Welcome to Paradise
          </h2>
          <p
            className="text-lg text-center max-w-3xl mx-auto  hover:text-[#8B4513] transition-all duration-300"
            style={{ animationDelay: "400ms" }}
          >
            Nestled in the heart of Nyerere National Park, Malombo Selous Forest
            Camp offers an unparalleled safari experience. Our eco-friendly
            lodge combines luxurious comfort with sustainable practices,
            allowing you to immerse yourself in Tanzania's pristine wilderness
            while treading lightly on the earth.
          </p>
        </div>
      </section>

      {/* Accommodations Section */}
      <section id="accommodation" className="py-16 bg-muted/30 w-[90%] mx-auto">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl text-center mb-12 playfair-display-heading text-[#8B4513] relative after:content-[''] after:block after:w-24 after:h-1 after:bg-[#DAA520] after:mx-auto after:mt-4">
            Our Accommodations
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {accommodations.map((accommodation) => (
              <Card
                key={accommodation.title}
                className="overflow-hidden group hover:shadow-2xl transition-all duration-500 bg-white/90 backdrop-blur-sm border-t-4 border-t-[#2C5530]"
              >
                <AspectRatio ratio={16 / 9}>
                  <img
                    src={accommodation.image}
                    alt={accommodation.title}
                    className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-105"
                  />
                </AspectRatio>
                <CardHeader className="bg-gradient-to-r from-[#2C5530]/5 to-transparent">
                  <CardTitle className="playfair-display-subheading text-2xl font-bold text-[#2C5530] group-hover:text-[#8B4513] transition-colors duration-300">
                    {accommodation.title}
                  </CardTitle>
                  <CardDescription className="text-neutral-600 mt-2 text-base">
                    {accommodation.description}
                  </CardDescription>
                </CardHeader>
                <CardFooter className="bg-gradient-to-r from-transparent to-[#2C5530]/5">
                  <Button
                    variant="outline"
                    className="w-full border-[#2C5530] text-[#2C5530] hover:bg-[#2C5530] hover:text-white transition-all duration-300"
                  >
                    View More
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>

          {/* View All Accommodations Button */}
          <div className="mt-12 text-center">
            <Button
              size="lg"
              className="bg-gradient-to-r from-[#2C5530] to-[#8B4513] hover:from-[#8B4513] hover:to-[#2C5530] text-white transition-all duration-300 px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              onClick={() => navigate("/accommodations")}
            >
              View All Accommodations
            </Button>
          </div>
        </div>
      </section>

      {/* Activities Section */}
      <section
        id="activities"
        className="py-16 w-[90%] mx-auto bg-gradient-to-b from-transparent to-muted/20"
        style={{ animationDelay: "400ms" }}
      >
        <div className="container mx-auto px-4">
          <h2
            className="text-4xl text-center mb-12 playfair-display-heading text-[#2C5530] relative
            after:content-[''] after:block after:w-24 after:h-1 after:bg-[#DAA520] after:mx-auto after:mt-4"
          >
            Featured Activities
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {activities.map((activity, index) => (
              <Card
                key={activity.title}
                className="overflow-hidden group bg-white/90 backdrop-blur-sm border-t-4 border-t-[#DAA520]
                  hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2"
                style={{ animationDelay: `${index * 200 + 600}ms` }}
              >
                <AspectRatio ratio={16 / 9} className="relative">
                  <img
                    src={activity.image}
                    alt={activity.title}
                    className="object-cover w-full h-full transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-black/20 group-hover:bg-black/10 transition-all duration-500" />
                </AspectRatio>
                <CardHeader className="bg-gradient-to-b from-[#DAA520]/5 to-transparent">
                  <CardTitle
                    className="playfair-display-subheading text-2xl font-bold text-[#DAA520] 
                    group-hover:text-[#8B4513] transition-all duration-300 border-b-2 border-[#DAA520]/20 pb-2"
                  >
                    {activity.title}
                  </CardTitle>
                  <CardDescription className="mt-4 text-neutral-600 group-hover:text-[#2C5530] transition-all duration-300 text-base">
                    {activity.description}
                  </CardDescription>
                </CardHeader>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section
        id="gallery"
        className="py-16 bg-gradient-to-b from-muted/30 to-muted/10 w-[90%] mx-auto"
      >
        <div className="container mx-auto px-4">
          <h2
            className="text-4xl text-center mb-12 playfair-display-heading text-[#8B4513] relative
            after:content-[''] after:block after:w-24 after:h-1 after:bg-[#2C5530] after:mx-auto after:mt-4"
          >
            Gallery
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {gallery.map((item, index) => (
              <Dialog key={index}>
                <DialogTrigger asChild>
                  <div
                    className="group relative overflow-hidden rounded-xl cursor-pointer 
                      hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <AspectRatio ratio={1}>
                      <img
                        src={item.image}
                        alt={item.title}
                        className="object-cover w-full h-full transition-transform duration-700 group-hover:scale-110"
                      />
                      <div
                        className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent 
                        opacity-0 group-hover:opacity-100 transition-all duration-500 flex flex-col justify-end p-4"
                      >
                        <h3
                          className="text-lg font-semibold playfair-display-subheading text-white transform translate-y-4 
                          group-hover:translate-y-0 transition-transform duration-500"
                        >
                          {item.title}
                        </h3>
                        <p
                          className="text-sm text-white/90 transform translate-y-4 group-hover:translate-y-0 
                          transition-transform duration-500 delay-100"
                        >
                          {item.description}
                        </p>
                      </div>
                    </AspectRatio>
                  </div>
                </DialogTrigger>
                <DialogContent className="max-w-4xl bg-white/95 backdrop-blur-sm">
                  <DialogHeader className="border-b-2 border-[#2C5530]/20 pb-4">
                    <DialogTitle className="playfair-display-heading text-2xl text-[#2C5530]">
                      {item.title}
                    </DialogTitle>
                    <DialogDescription className="text-base text-neutral-600">
                      {item.description}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="mt-4">
                    <img
                      src={item.image}
                      alt={item.title}
                      className="w-full h-auto rounded-lg shadow-lg"
                    />
                  </div>
                </DialogContent>
              </Dialog>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 w-[90%] mx-auto">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl text-center mb-12 playfair-display-heading text-[#2C5530]">
            Guest Experiences
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-primary/5">
                <CardHeader>
                  <CardDescription className="text-lg italic">
                    "{testimonial.quote}"
                  </CardDescription>
                </CardHeader>
                <CardFooter className="text-sm font-medium">
                  - {testimonial.author}
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section
        id="contact"
        className="py-16 bg-gradient-to-b from-muted/50 to-muted w-[90%] mx-auto"
      >
        <div className="container mx-auto px-4">
          <h2 className="text-3xl text-center mb-12 playfair-display-heading text-[#8B4513] animate-fade-in-up">
            Get in Touch
          </h2>
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Location Card */}
              <Card className="group hover:shadow-xl transition-all duration-300 bg-white/50 backdrop-blur-sm border-t-4 border-t-[#2C5530] overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-[#2C5530]/10 to-transparent pb-4">
                  <CardTitle className="text-[#2C5530] flex items-center gap-3">
                    <div className="p-3 bg-[#2C5530] rounded-full">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6 text-white"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 1 1 16 0Z" />
                        <circle cx="12" cy="10" r="3" />
                      </svg>
                    </div>
                    <span className="text-xl playfair-display-heading">
                      Visit Us
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  <p className="text-neutral-600 group-hover:text-[#8B4513] transition-colors duration-300 space-y-2">
                    <span className="block font-semibold text-[#2C5530]">
                      Malombo Selous Forest Camp
                    </span>
                    <span className="block">500m from Mloka village</span>
                    <span className="block">6.8km from Mtemere Gate</span>
                    <span className="block">Nyerere National Park</span>
                    <span className="block">Tanzania, East Africa</span>
                  </p>
                </CardContent>
              </Card>

              {/* Email Card */}
              <Card className="group hover:shadow-xl transition-all duration-300 bg-white/50 backdrop-blur-sm border-t-4 border-t-[#DAA520] overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-[#DAA520]/10 to-transparent pb-4">
                  <CardTitle className="text-[#DAA520] flex items-center gap-3">
                    <div className="p-3 bg-[#DAA520] rounded-full">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6 text-white"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <rect width="20" height="16" x="2" y="4" rx="2" />
                        <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
                      </svg>
                    </div>
                    <span className="text-xl playfair-display-heading">
                      Email Us
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="space-y-3">
                    <p className="text-neutral-600 mb-2">
                      For bookings and inquiries:
                    </p>
                    <a
                      href="mailto:<EMAIL>"
                      className="text-[#DAA520] group-hover:text-[#8B4513] transition-colors duration-300 hover:underline flex items-center gap-2 text-lg"
                    >
                      <EMAIL>
                    </a>
                    <p className="text-sm text-neutral-500">
                      We respond to all emails within 24 hours
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Phone Card */}
              <Card className="group hover:shadow-xl transition-all duration-300 bg-white/50 backdrop-blur-sm border-t-4 border-t-[#8B4513] overflow-hidden">
                <CardHeader className="bg-gradient-to-r from-[#8B4513]/10 to-transparent pb-4">
                  <CardTitle className="text-[#8B4513] flex items-center gap-3">
                    <div className="p-3 bg-[#8B4513] rounded-full">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-6 w-6 text-white"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <path d="M5 4h4l2 5l-2.5 1.5a11 11 0 0 0 5 5L15 13l5 2v4a2 2 0 0 1-2 2A16 16 0 0 1 3 6a2 2 0 0 1 2-2" />
                        <path d="M15 7a2 2 0 0 1 2 2" />
                        <path d="M15 3a6 6 0 0 1 6 6" />
                      </svg>
                    </div>
                    <span className="text-xl playfair-display-heading">
                      Call Us
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="space-y-3">
                    <p className="text-neutral-600 mb-2">Available 24/7:</p>
                    <a
                      href="tel:+255123456789"
                      className="text-[#8B4513] group-hover:text-[#DAA520] transition-colors duration-300 hover:underline flex items-center gap-2 text-lg"
                    >
                      +255 123 456 789
                    </a>
                    <p className="text-sm text-neutral-500">
                      International & WhatsApp available
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Booking Button Section */}
            <div className="mt-12 text-center">
              <Button
                size="lg"
                className="transition-all duration-300 bg-gradient-to-r from-[#b3840e] to-[#8B4513] hover:from-0% hover:to-10% text-white hover:scale-105 shadow-lg hover:shadow-xl"
              >
                Book Your Stay Now
              </Button>
              <p className="mt-4 text-sm text-muted-foreground">
                We'll get back to you within 24 hours
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

const accommodations = [
  {
    title: "Luxury Bandas",
    description:
      "Traditional-style cottages with modern amenities and private verandas",
    image: "/images/bandas.jpg",
  },
  {
    title: "Tree House",
    description:
      "Unique elevated accommodation with panoramic wilderness views",
    image: "/images/treehouse.jpg",
  },
  {
    title: "Camping Site",
    description:
      "Premium camping experience with full amenities and staff support",
    image: "/images/camping.jpg",
  },
];

const activities = [
  {
    title: "Game Drives",
    description:
      "Explore the wilderness with our expert guides in custom safari vehicles",
    image: "/images/game-drive.jpg",
  },
  {
    title: "Boat Safari",
    description:
      "Cruise along the mighty Rufiji River observing wildlife up close",
    image: "/images/boat-safari.jpg",
  },
  {
    title: "Walking Safari",
    description: "Experience the bush on foot with our experienced rangers",
    image: "/images/walking-safari.jpg",
  },
];

const gallery = [
  {
    image: "/images/gallery-1.jpg",
    title: "Sunrise Safari",
    description: "Early morning game drive through the savanna",
  },
  {
    image: "/images/gallery-2.jpg",
    title: "Luxury Accommodation",
    description: "Our comfortable and stylish bandas",
  },
  {
    image: "/images/gallery-3.jpg",
    title: "River Adventure",
    description: "Boat safari on the mighty Rufiji River",
  },
  {
    image: "/images/gallery-4.jpg",
    title: "Wildlife Encounters",
    description: "Up-close experiences with African wildlife",
  },
  {
    image: "/images/gallery-5.jpg",
    title: "Sundowner",
    description: "Evening drinks with a spectacular view",
  },
  {
    image: "/images/gallery-6.jpg",
    title: "Bush Dining",
    description: "Authentic outdoor dining experience",
  },
  {
    image: "/images/gallery-7.jpg",
    title: "Cultural Visit",
    description: "Engaging with local communities",
  },
  {
    image: "/images/gallery-8.jpg",
    title: "Night Safari",
    description: "Exploring the wilderness after dark",
  },
];

const testimonials = [
  {
    quote:
      "A truly magical safari experience. The staff made us feel like family!",
    author: "Sarah & John, UK",
  },
  {
    quote: "Perfect blend of luxury and authentic wilderness experience.",
    author: "Michael & Lisa, USA",
  },
  {
    quote: "The wildlife viewing exceeded all our expectations. We'll be back!",
    author: "Hans & Maria, Germany",
  },
];

export default Home;
