import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/hooks/use-auth";
import { isStaffOrAdmin } from "@/lib/auth";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAdmin = false,
}) => {
  const { user, profile, loading } = useAuth();
  const location = useLocation();

  // Debug logging for development
  if (process.env.NODE_ENV === "development") {
    console.log("ProtectedRoute state:", {
      hasUser: !!user,
      hasProfile: !!profile,
      profileRole: profile?.role,
      loading,
    });
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-amber-600"></div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/admin/login" state={{ from: location }} replace />;
  }

  if (!profile || !isStaffOrAdmin(profile)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Access Denied
          </h1>
          <p className="text-gray-600">
            You don't have permission to access this area.
          </p>
          {process.env.NODE_ENV === "development" && (
            <div className="mt-4 text-sm text-gray-500">
              Debug: User ID: {user?.id}, Profile:{" "}
              {profile ? JSON.stringify(profile) : "null"}
            </div>
          )}
        </div>
      </div>
    );
  }

  if (requireAdmin && profile.role !== "admin") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Admin Access Required
          </h1>
          <p className="text-gray-600">
            This area requires administrator privileges.
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};
