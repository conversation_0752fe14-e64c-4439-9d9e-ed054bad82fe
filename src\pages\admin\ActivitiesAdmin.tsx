import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Plus, Calendar, Edit, Trash2, Clock, Users } from 'lucide-react'

export default function ActivitiesAdmin() {
  const activities = [
    { 
      id: 1, 
      name: 'Morning Game Drive', 
      duration: '3 hours', 
      capacity: 8, 
      price: '$120',
      status: 'Active'
    },
    { 
      id: 2, 
      name: 'Sunset Safari', 
      duration: '4 hours', 
      capacity: 6, 
      price: '$150',
      status: 'Active'
    },
    { 
      id: 3, 
      name: 'Walking Safari', 
      duration: '2 hours', 
      capacity: 4, 
      price: '$80',
      status: 'Inactive'
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Activities</h1>
          <p className="text-gray-600 mt-2">Manage safari experiences and tours</p>
        </div>
        <Button className="bg-amber-600 hover:bg-amber-700">
          <Plus className="w-4 h-4 mr-2" />
          Add Activity
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {activities.map((activity) => (
          <Card key={activity.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <Calendar className="w-8 h-8 text-green-600" />
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  activity.status === 'Active' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {activity.status}
                </span>
              </div>
              <CardTitle className="text-lg">{activity.name}</CardTitle>
              <CardDescription className="text-2xl font-bold text-amber-600">
                {activity.price}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="w-4 h-4 mr-2" />
                  {activity.duration}
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Users className="w-4 h-4 mr-2" />
                  Up to {activity.capacity} guests
                </div>
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
                <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Activity Performance</CardTitle>
          <CardDescription>Booking statistics for this month</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">6</div>
              <div className="text-sm text-gray-500">Active Activities</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-600">42</div>
              <div className="text-sm text-gray-500">Total Bookings</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">$3,240</div>
              <div className="text-sm text-gray-500">Revenue</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">4.8</div>
              <div className="text-sm text-gray-500">Avg Rating</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
