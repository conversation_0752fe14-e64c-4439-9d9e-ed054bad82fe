# Deployment Guide - <PERSON><PERSON><PERSON> Selous Forest Camp Website

This guide covers deployment configurations for various hosting platforms to ensure proper SPA (Single Page Application) routing without 404 errors.

## 🚀 Quick Start

The website is configured to work with client-side routing. When deployed, all routes (`/`, `/accommodations`, `/booking`) should serve the same `index.html` file, allowing React Router to handle navigation.

## 📋 Pre-Deployment Checklist

- [ ] Build the application: `npm run build`
- [ ] Test the build locally: `npm run preview`
- [ ] Verify all routes work correctly
- [ ] Check that page reloads don't cause 404 errors

## 🌐 Platform-Specific Configurations

### 1. Netlify

**Configuration File:** `public/_redirects`

```bash
# Deploy to Netlify
npm run build
# Upload the 'dist' folder to Netlify
```

**Features:**
- ✅ Automatic SPA routing
- ✅ Static asset caching
- ✅ HTTPS enforcement options
- ✅ Custom domain support

### 2. Vercel

**Configuration File:** `vercel.json`

```bash
# Deploy to Vercel
npm run build
vercel --prod
```

**Features:**
- ✅ Automatic SPA routing
- ✅ Edge caching
- ✅ Custom headers
- ✅ Performance optimization

### 3. GitHub Pages

**Configuration Files:** `public/404.html` + GitHub Pages script in `index.html`

```bash
# Build for GitHub Pages
npm run build
# Deploy the 'dist' folder to gh-pages branch
```

**Setup Steps:**
1. Enable GitHub Pages in repository settings
2. Set source to gh-pages branch
3. The 404.html file will handle routing automatically

### 4. Apache Server

**Configuration File:** `public/.htaccess`

```bash
# Upload the built files to your Apache server
# The .htaccess file will be automatically copied
```

**Features:**
- ✅ URL rewriting for SPA routing
- ✅ Security headers
- ✅ Gzip compression
- ✅ Static asset caching

### 5. IIS (Windows Server)

**Configuration File:** `public/web.config`

```bash
# Upload the built files to your IIS server
# The web.config file will be automatically copied
```

**Features:**
- ✅ URL rewriting for SPA routing
- ✅ Security headers
- ✅ Compression
- ✅ Static file caching

### 6. Nginx

**Manual Configuration Required:**

Add this to your Nginx server configuration:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/your/dist/folder;
    index index.html;

    # Handle client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
}
```

## 🔧 Build Configuration

The `vite.config.ts` includes:

- **History API Fallback:** Ensures dev server handles SPA routing
- **Manual Chunks:** Optimizes bundle splitting
- **Preview Configuration:** Handles SPA routing in preview mode

## 🛠️ Error Handling

### Custom 404 Page
- Route: `*` (catch-all)
- Component: `src/pages/NotFound.tsx`
- Features: Navigation help, contact info, quick links

### Error Boundary
- Component: `src/components/ErrorBoundary.tsx`
- Catches JavaScript errors
- Provides user-friendly error messages
- Shows detailed errors in development

## 🧪 Testing Deployment

### Local Testing
```bash
# Build and test locally
npm run build
npm run preview

# Test these URLs manually:
# http://localhost:4173/
# http://localhost:4173/accommodations
# http://localhost:4173/booking
# http://localhost:4173/non-existent-page
```

### Production Testing
After deployment, test these scenarios:
1. Direct navigation to `/accommodations`
2. Page reload on `/booking`
3. Invalid URLs (should show 404 page)
4. Browser back/forward navigation

## 🔍 Troubleshooting

### Common Issues

**404 Errors on Page Reload:**
- Ensure server configuration redirects all routes to `index.html`
- Check that the appropriate config file is deployed

**Assets Not Loading:**
- Verify build output includes all static files
- Check asset paths in the built files

**Routing Not Working:**
- Confirm React Router is properly configured
- Check browser console for JavaScript errors

### Debug Steps

1. **Check Network Tab:** Look for 404s on route changes
2. **Verify Config Files:** Ensure platform-specific files are deployed
3. **Test Build Locally:** Use `npm run preview` to test production build
4. **Check Server Logs:** Look for routing-related errors

## 📊 Performance Optimization

### Included Optimizations

- **Code Splitting:** Vendor and router chunks separated
- **Static Asset Caching:** Long-term caching for images, fonts, CSS, JS
- **Compression:** Gzip/Brotli compression enabled
- **Security Headers:** XSS protection, content type sniffing prevention

### Monitoring

After deployment, monitor:
- Page load times
- 404 error rates
- User navigation patterns
- Core Web Vitals

## 🔐 Security Considerations

All configurations include:
- **XSS Protection:** Prevents cross-site scripting
- **Content Type Sniffing:** Prevents MIME type attacks
- **Frame Options:** Prevents clickjacking
- **HTTPS Enforcement:** Redirects HTTP to HTTPS (where applicable)

## 📞 Support

If you encounter deployment issues:
1. Check this guide first
2. Review platform-specific documentation
3. Test locally with `npm run preview`
4. Contact the development team with specific error messages

---

**Note:** This website is a Single Page Application (SPA) built with React and Vite. All routing is handled client-side, so server configuration is crucial for proper functionality.
