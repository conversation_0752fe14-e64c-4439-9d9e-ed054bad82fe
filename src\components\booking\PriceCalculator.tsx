import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Calendar, Users, MapPin } from "lucide-react";
import type { PricingBreakdown } from "@/types/booking";

interface PriceCalculatorProps {
  pricing: PricingBreakdown;
  accommodationName: string;
  checkIn: Date;
  checkOut: Date;
  guestCount: number;
  activitiesCount: number;
  isCompact?: boolean;
}

export function PriceCalculator({
  pricing,
  accommodationName,
  checkIn,
  checkOut,
  guestCount,
  activitiesCount,
  isCompact = false,
}: PriceCalculatorProps) {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  if (isCompact) {
    return (
      <Card className="border-[#2C5530] bg-[#2C5530]/5">
        <CardContent className="p-4">
          <div className="flex justify-between items-center mb-2">
            <span className="font-semibold text-[#2C5530]">Total Cost:</span>
            <span className="text-2xl font-bold text-[#8B4513]">
              ${pricing.total.toLocaleString()}
            </span>
          </div>
          <div className="text-sm text-neutral-600">
            {pricing.nights} nights • {guestCount} guests
            {activitiesCount > 0 && ` • ${activitiesCount} activities`}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-[#2C5530] bg-gradient-to-b from-white to-[#2C5530]/5 sticky top-4">
      <CardHeader className="bg-[#2C5530] text-white">
        <CardTitle className="playfair-display-heading text-xl">
          Booking Summary
        </CardTitle>
      </CardHeader>

      <CardContent className="p-6 space-y-6">
        {/* Booking Details */}
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <MapPin className="h-5 w-5 text-[#2C5530]" />
            <div>
              <div className="font-semibold text-[#2C5530]">
                {accommodationName}
              </div>
              <div className="text-sm text-neutral-600">
                Malombo Selous Forest Camp
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Calendar className="h-5 w-5 text-[#2C5530]" />
            <div>
              <div className="font-semibold text-[#2C5530]">
                {formatDate(checkIn)} - {formatDate(checkOut)}
              </div>
              <div className="text-sm text-neutral-600">
                {pricing.nights} night{pricing.nights !== 1 ? "s" : ""}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Users className="h-5 w-5 text-[#2C5530]" />
            <div>
              <div className="font-semibold text-[#2C5530]">
                {guestCount} Guests
              </div>
              <div className="text-sm text-neutral-600">Total occupancy</div>
            </div>
          </div>
        </div>

        <Separator />

        {/* Price Breakdown */}
        <div className="space-y-3">
          <h4 className="font-semibold text-[#2C5530] mb-3">Price Breakdown</h4>

          {/* Accommodation */}
          <div className="flex justify-between items-center">
            <div>
              <div className="text-sm">{accommodationName}</div>
              <div className="text-xs text-neutral-500">
                ${pricing.accommodationPrice}/night × {pricing.nights} nights
              </div>
            </div>
            <div className="font-semibold">
              ${pricing.accommodationTotal.toLocaleString()}
            </div>
          </div>

          {/* Activities */}
          {pricing.activitiesTotal > 0 && (
            <div className="flex justify-between items-center">
              <div>
                <div className="text-sm">Activities</div>
                <div className="text-xs text-neutral-500">
                  {activitiesCount} selected activities
                </div>
              </div>
              <div className="font-semibold">
                ${pricing.activitiesTotal.toLocaleString()}
              </div>
            </div>
          )}

          <Separator />

          {/* Subtotal */}
          <div className="flex justify-between items-center">
            <span className="text-sm">Subtotal</span>
            <span className="font-semibold">
              ${pricing.subtotal.toLocaleString()}
            </span>
          </div>

          {/* Taxes */}
          <div className="flex justify-between items-center">
            <div>
              <span className="text-sm">Taxes & Fees</span>
              <div className="text-xs text-neutral-500">18% VAT included</div>
            </div>
            <span className="font-semibold">
              ${pricing.taxes.toLocaleString()}
            </span>
          </div>

          <Separator className="border-[#2C5530]" />

          {/* Total */}
          <div className="flex justify-between items-center bg-[#2C5530]/10 p-3 rounded-lg">
            <div>
              <span className="text-lg font-bold text-[#2C5530]">Total</span>
              <div className="text-xs text-neutral-600">All inclusive</div>
            </div>
            <span className="text-2xl font-bold text-[#8B4513]">
              ${pricing.total.toLocaleString()}
            </span>
          </div>
        </div>

        {/* Savings Badge */}
        {pricing.nights >= 3 && (
          <div className="text-center">
            <Badge className="bg-[#DAA520] text-white">
              🎉 3+ nights: 10% discount applied!
            </Badge>
          </div>
        )}

        {/* Payment Info */}
        <div className="text-center text-xs text-neutral-500 bg-neutral-50 p-3 rounded">
          <p className="mb-1">💳 Secure payment processing</p>
          <p>📞 24/7 customer support included</p>
        </div>
      </CardContent>
    </Card>
  );
}
