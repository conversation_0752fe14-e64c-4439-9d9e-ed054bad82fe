import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check } from "lucide-react";

interface BookingStepProps {
  stepNumber: number;
  title: string;
  description: string;
  isActive: boolean;
  isComplete: boolean;
  children?: React.ReactNode;
}

export function BookingStep({
  stepNumber,
  title,
  description,
  isActive,
  isComplete,
  children,
}: BookingStepProps) {
  return (
    <div className="w-full">
      {/* Step Header */}
      <div className="flex items-center gap-4 mb-6">
        <div
          className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
            isComplete
              ? "bg-[#2C5530] border-[#2C5530] text-white"
              : isActive
              ? "bg-[#DAA520] border-[#DAA520] text-white"
              : "bg-white border-neutral-300 text-neutral-500"
          }`}
        >
          {isComplete ? (
            <Check className="h-6 w-6" />
          ) : (
            <span className="text-lg font-bold">{stepNumber}</span>
          )}
        </div>
        <div className="flex-1">
          <h2 className="text-2xl font-bold playfair-display-heading text-[#2C5530]">
            {title}
          </h2>
          <p className="text-neutral-600">{description}</p>
        </div>
        <Badge
          variant={isActive ? "default" : isComplete ? "secondary" : "outline"}
          className={
            isActive
              ? "bg-[#DAA520] text-white"
              : isComplete
              ? "bg-[#2C5530] text-white"
              : ""
          }
        >
          {isComplete ? "Complete" : isActive ? "Current" : "Pending"}
        </Badge>
      </div>

      {/* Step Content */}
      {(isActive || isComplete) && (
        <Card className="border-t-4 border-t-[#2C5530] shadow-lg">
          <CardContent className="p-6">{children}</CardContent>
        </Card>
      )}
    </div>
  );
}

interface StepProgressProps {
  currentStep: number;
  totalSteps: number;
  steps: Array<{ title: string; description: string; sn: number }>;
}

export function StepProgress({
  currentStep,
  totalSteps,
  steps,
}: StepProgressProps) {
  return (
    <div className="mb-8">
      <div className="flex items-center md:justify-center px-2 overflow-x-auto mb-4">
        {steps.map((step) => {
          const stepNumber = step.sn + 1;
          const isActive = stepNumber === currentStep;
          const isComplete = stepNumber < currentStep;

          return (
            <div key={stepNumber} className="flex items-center">
              <div
                className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300 ${
                  isComplete
                    ? "bg-[#2C5530] border-[#2C5530] text-white"
                    : isActive
                    ? "bg-[#DAA520] border-[#DAA520] text-white"
                    : "bg-white border-neutral-300 text-neutral-500"
                }`}
              >
                {isComplete ? (
                  <Check className="h-5 w-5" />
                ) : (
                  <span className="text-sm font-bold">{stepNumber}</span>
                )}
              </div>

              {stepNumber < totalSteps && (
                <div
                  className={`w-16 h-1 mx-2 transition-all duration-300 ${
                    stepNumber < currentStep ? "bg-[#2C5530]" : "bg-neutral-300"
                  }`}
                />
              )}
            </div>
          );
        })}
      </div>

      <div className="text-center">
        <h3 className="text-lg font-semibold text-[#2C5530]">
          Step {currentStep} of {totalSteps}: {steps[currentStep - 1]?.title}
        </h3>
        <p className="text-neutral-600 text-sm">
          {steps[currentStep - 1]?.description}
        </p>
      </div>
    </div>
  );
}
