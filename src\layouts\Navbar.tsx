import { useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import logoImage from "@/assets/logo.png";

const navLinks = [
  { href: "#home", label: "Home", type: "scroll" },
  { href: "#about", label: "About", type: "scroll" },
  { href: "/accommodations", label: "Accommodations", type: "route" },
  { href: "#activities", label: "Activities", type: "scroll" },
  { href: "#gallery", label: "Gallery", type: "scroll" },
  { href: "#contact", label: "Contact", type: "scroll" },
];

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const location = useLocation();

  const handleNavClick = (link: (typeof navLinks)[0]) => {
    if (link.type === "route") {
      navigate(link.href);
    } else {
      // For scroll navigation, first navigate to home if not already there
      if (location.pathname !== "/") {
        navigate("/");
        // Wait for navigation to complete, then scroll
        setTimeout(() => {
          const element = document.querySelector(link.href);
          if (element) {
            element.scrollIntoView({ behavior: "smooth" });
          }
        }, 100);
      } else {
        const element = document.querySelector(link.href);
        if (element) {
          element.scrollIntoView({ behavior: "smooth" });
        }
      }
    }
  };

  return (
    <nav className="fixed left-0 top-0 w-full bg-white/70 backdrop-blur-lg border-b border-neutral-200 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center h-16 justify-between">
          {/* Logo */}
          <div className="flex-shrink-0">
            <button onClick={() => navigate("/")} className="flex items-center">
              <img
                src={logoImage}
                alt="Malombo Selous Forest Camp"
                className="h-10 w-auto"
              />
            </button>
          </div>

          {/* Desktop Navigation - Centered */}
          {!isMobile && (
            <div className="hidden md:flex items-center justify-center flex-1 px-8">
              {navLinks.map((link) => (
                <button
                  key={link.href}
                  onClick={() => handleNavClick(link)}
                  className="text-neutral-900 hover:text-amber-700 transition-colors duration-200 whitespace-nowrap px-3"
                >
                  {link.label}
                </button>
              ))}
            </div>
          )}

          {/* CTA Button */}
          {!isMobile && (
            <div className="hidden md:flex flex-shrink-0 ml-auto">
              <Button
                variant="default"
                className="bg-amber-700 hover:bg-amber-800 text-white"
              >
                Book Now
              </Button>
            </div>
          )}

          {/* Mobile Navigation */}
          {isMobile && (
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="hover:bg-amber-50 transition-colors duration-200 rounded-full p-2"
                >
                  <Menu className="h-6 w-6 text-amber-800" />
                </Button>
              </SheetTrigger>
              <SheetContent
                side="right"
                className="w-[300px] sm:w-[400px] bg-gradient-to-b from-white to-amber-50 overflow-y-auto flex flex-col p-0"
              >
                {/* Main content container with scroll */}
                <div className="flex-1 overflow-y-auto">
                  <div className="p-6 flex flex-col min-h-full">
                    {/* Logo in menu */}
                    <div className="mb-8 flex flex-col items-center">
                      <img
                        src={logoImage}
                        alt="Malombo Selous Forest Camp"
                        className="h-12 w-auto mb-2"
                      />
                      <p className="text-sm text-neutral-600 text-center">
                        Selous Forest Camp
                      </p>
                    </div>

                    {/* Navigation Links */}
                    <div className="flex flex-col mb-8">
                      {navLinks.map((link, index) => (
                        <button
                          key={link.href}
                          className="text-neutral-800 hover:text-amber-700 hover:bg-amber-50/80
                                   transition-all duration-200 py-4 px-6 border-b border-amber-100/30
                                   hover:border-amber-200 flex items-center justify-between group w-full text-left"
                          onClick={() => {
                            setIsOpen(false);
                            handleNavClick(link);
                          }}
                          style={{ animationDelay: `${index * 100}ms` }}
                        >
                          <span className="text-lg playfair-display-subheading">
                            {link.label}
                          </span>
                          <span className="opacity-0 group-hover:opacity-100 transform group-hover:translate-x-0 -translate-x-4 transition-all duration-200">
                            →
                          </span>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Book Now Section - Fixed at bottom */}
                <div className="sticky bottom-0 w-full bg-white p-4 border-t border-amber-100">
                  <div className="px-4 py-4 bg-amber-50/80 rounded-lg backdrop-blur-sm">
                    <p className="text-sm text-neutral-600 mb-4">
                      Ready for an unforgettable safari experience?
                    </p>
                    <Button
                      variant="default"
                      className="bg-gradient-to-r from-amber-700 to-amber-800 hover:from-amber-800 hover:to-amber-900 
                               text-white w-full py-6 shadow-lg hover:shadow-xl transition-all duration-300
                               text-lg font-semibold"
                      onClick={() => setIsOpen(false)}
                    >
                      Book Your Stay
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          )}
        </div>
      </div>
    </nav>
  );
}
