import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Plus, Building, Edit, Trash2 } from 'lucide-react'

export default function AccommodationsAdmin() {
  const accommodations = [
    { id: 1, name: 'Safari Suite', type: 'Suite', capacity: 2, status: 'Available' },
    { id: 2, name: 'Forest View Room', type: 'Room', capacity: 2, status: 'Occupied' },
    { id: 3, name: 'Riverside Tent', type: 'Tent', capacity: 4, status: 'Available' },
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Accommodations</h1>
          <p className="text-gray-600 mt-2">Manage rooms, suites, and tents</p>
        </div>
        <Button className="bg-amber-600 hover:bg-amber-700">
          <Plus className="w-4 h-4 mr-2" />
          Add Accommodation
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {accommodations.map((accommodation) => (
          <Card key={accommodation.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <Building className="w-8 h-8 text-amber-600" />
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  accommodation.status === 'Available' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {accommodation.status}
                </span>
              </div>
              <CardTitle className="text-lg">{accommodation.name}</CardTitle>
              <CardDescription>
                {accommodation.type} • Capacity: {accommodation.capacity} guests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
                <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Accommodation Statistics</CardTitle>
          <CardDescription>Overview of accommodation usage</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">8</div>
              <div className="text-sm text-gray-500">Available</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">4</div>
              <div className="text-sm text-gray-500">Occupied</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">12</div>
              <div className="text-sm text-gray-500">Total</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
