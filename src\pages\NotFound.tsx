import { useNavigate } from "react-router-dom";
import { usePageTitle } from "@/hooks/use-page-title";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Home, ArrowLeft, MapPin, Phone, Mail } from "lucide-react";

const NotFound = () => {
  const navigate = useNavigate();
  usePageTitle("Page Not Found");

  return (
    <div className="flex flex-col w-full min-h-screen">
      {/* Hero Section */}
      <section className="relative w-full h-[60vh] bg-gradient-to-r from-[#8B4513] to-[#DAA520]">
        <div className="absolute inset-0">
          <img
            src="/images/safari-landscape.jpg"
            alt="Safari Landscape"
            className="w-full h-full object-cover opacity-30"
          />
        </div>
        <div className="relative z-10 container mx-auto h-full flex flex-col justify-center items-center px-4 text-center">
          <div className="text-8xl mb-6">🦁</div>
          <h1 className="text-5xl md:text-6xl mb-4 playfair-display-heading text-white">
            Oops! <span className="text-[#DAA520]">Lost in the Wilderness</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-3xl playfair-display-subheading text-white/90">
            The page you're looking for seems to have wandered off into the African savanna
          </p>
        </div>
      </section>

      {/* Content Section */}
      <section className="py-16 w-[95%] mx-auto flex-grow">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Error Information */}
            <div className="space-y-6">
              <Card className="border-t-4 border-t-[#8B4513]">
                <CardHeader>
                  <CardTitle className="playfair-display-heading text-2xl text-[#8B4513] flex items-center gap-3">
                    <MapPin className="h-6 w-6" />
                    Page Not Found (404)
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-neutral-600 leading-relaxed">
                    Don't worry! Even the most experienced safari guides sometimes take a wrong turn. 
                    The page you're looking for might have been moved, deleted, or the URL might be incorrect.
                  </p>
                  
                  <div className="space-y-3">
                    <h4 className="font-semibold text-[#8B4513]">Here's what you can do:</h4>
                    <ul className="space-y-2 text-sm text-neutral-600">
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#DAA520] rounded-full" />
                        Check the URL for any typos
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#DAA520] rounded-full" />
                        Use the navigation menu to find what you're looking for
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#DAA520] rounded-full" />
                        Return to our homepage and start fresh
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#DAA520] rounded-full" />
                        Contact us if you think this is an error
                      </li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  onClick={() => navigate(-1)}
                  variant="outline"
                  className="border-[#8B4513] text-[#8B4513] hover:bg-[#8B4513] hover:text-white flex items-center gap-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  Go Back
                </Button>
                <Button
                  size="lg"
                  onClick={() => navigate('/')}
                  className="bg-gradient-to-r from-[#8B4513] to-[#DAA520] hover:from-[#DAA520] hover:to-[#8B4513] text-white flex items-center gap-2"
                >
                  <Home className="h-4 w-4" />
                  Return Home
                </Button>
              </div>
            </div>

            {/* Quick Navigation */}
            <div className="space-y-6">
              <h3 className="text-2xl playfair-display-heading text-[#8B4513] mb-6">
                Quick Navigation
              </h3>
              
              <div className="grid grid-cols-1 gap-4">
                <Card 
                  className="cursor-pointer hover:shadow-lg transition-all duration-300 border-l-4 border-l-[#8B4513]"
                  onClick={() => navigate('/')}
                >
                  <CardContent className="p-4">
                    <h4 className="font-semibold text-[#8B4513] mb-2">🏠 Home</h4>
                    <p className="text-sm text-neutral-600">Discover our safari camp and wilderness experience</p>
                  </CardContent>
                </Card>

                <Card 
                  className="cursor-pointer hover:shadow-lg transition-all duration-300 border-l-4 border-l-[#DAA520]"
                  onClick={() => navigate('/accommodations')}
                >
                  <CardContent className="p-4">
                    <h4 className="font-semibold text-[#8B4513] mb-2">🏕️ Accommodations</h4>
                    <p className="text-sm text-neutral-600">Explore our luxury bandas, tree house, and camping options</p>
                  </CardContent>
                </Card>

                <Card 
                  className="cursor-pointer hover:shadow-lg transition-all duration-300 border-l-4 border-l-[#8B4513]"
                  onClick={() => navigate('/booking')}
                >
                  <CardContent className="p-4">
                    <h4 className="font-semibold text-[#8B4513] mb-2">📅 Booking</h4>
                    <p className="text-sm text-neutral-600">Reserve your African safari adventure</p>
                  </CardContent>
                </Card>
              </div>

              {/* Contact Information */}
              <Card className="bg-gradient-to-br from-[#8B4513]/5 to-[#DAA520]/5 border-[#8B4513]/20">
                <CardHeader>
                  <CardTitle className="text-lg text-[#8B4513]">Need Help?</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-3 text-sm">
                    <Mail className="h-4 w-4 text-[#8B4513]" />
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center gap-3 text-sm">
                    <Phone className="h-4 w-4 text-[#8B4513]" />
                    <span>+255 123 456 789</span>
                  </div>
                  <p className="text-xs text-neutral-600 mt-3">
                    Our team is available 24/7 to assist you with any questions or booking inquiries.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default NotFound;
