-- =====================================================
-- Malombo Admin Panel - Database Triggers
-- =====================================================
-- This file creates database triggers for automatic
-- timestamp updates and other automated operations.
-- 
-- Safe to run multiple times (idempotent)
-- =====================================================

-- =====================================================
-- FUNCTION: Update timestamp on row modification
-- =====================================================

-- Drop function if it exists (for idempotency)
DROP FUNCTION IF EXISTS public.update_updated_at_column() CASCADE;

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER 
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
    -- Set the updated_at column to current timestamp
    NEW.updated_at = NOW();
    
    -- Log the update (optional, for debugging)
    -- RAISE NOTICE 'Updated timestamp for table: %, id: %', TG_TABLE_NAME, NEW.id;
    
    RETURN NEW;
END;
$$;

-- Add comment to function
COMMENT ON FUNCTION public.update_updated_at_column() IS 'Automatically updates updated_at timestamp when a row is modified';

-- =====================================================
-- FUNCTION: Handle new user registration (Alternative Approach)
-- =====================================================
-- Note: Direct triggers on auth.users are not allowed in Supabase
-- Instead, we'll use a different approach for automatic profile creation

-- Drop function if it exists (for idempotency)
DROP FUNCTION IF EXISTS public.handle_new_user() CASCADE;

-- Create function that can be called manually or via webhook
CREATE OR REPLACE FUNCTION public.create_profile_for_user(
    user_id UUID,
    user_email TEXT,
    user_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS VOID
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
    -- Insert a new profile record for the user
    INSERT INTO public.profiles (id, email, role, first_name, last_name)
    VALUES (
        user_id,
        user_email,
        'staff', -- Default role is staff
        COALESCE(user_metadata->>'first_name', ''),
        COALESCE(user_metadata->>'last_name', '')
    )
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        first_name = COALESCE(EXCLUDED.first_name, profiles.first_name),
        last_name = COALESCE(EXCLUDED.last_name, profiles.last_name),
        updated_at = NOW();

    -- Log the profile creation
    RAISE NOTICE 'Created/updated profile for user: %', user_email;

EXCEPTION
    WHEN OTHERS THEN
        -- Log error and re-raise
        RAISE WARNING 'Failed to create profile for user %: %', user_email, SQLERRM;
        RAISE;
END;
$$;

-- Add comment to function
COMMENT ON FUNCTION public.create_profile_for_user(UUID, TEXT, JSONB) IS 'Creates or updates a profile record for a user (call manually or via webhook)';

-- =====================================================
-- FUNCTION: Update last login timestamp (Manual Call)
-- =====================================================

-- Drop function if it exists (for idempotency)
DROP FUNCTION IF EXISTS public.update_last_login() CASCADE;

-- Create function to update last login timestamp (call from application)
CREATE OR REPLACE FUNCTION public.update_user_last_login(user_id UUID)
RETURNS VOID
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
    -- Update last_login_at for the specified user
    UPDATE public.profiles
    SET last_login_at = NOW(),
        updated_at = NOW()
    WHERE id = user_id;

    -- Check if update was successful
    IF NOT FOUND THEN
        RAISE WARNING 'No profile found for user ID: %', user_id;
    ELSE
        RAISE NOTICE 'Updated last login for user: %', user_id;
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        -- Log error and re-raise
        RAISE WARNING 'Failed to update last login for user %: %', user_id, SQLERRM;
        RAISE;
END;
$$;

-- Add comment to function
COMMENT ON FUNCTION public.update_user_last_login(UUID) IS 'Updates last_login_at timestamp for a user (call from application after login)';

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Drop existing triggers if they exist (for idempotency)
DROP TRIGGER IF EXISTS trigger_update_profiles_updated_at ON public.profiles;

-- Trigger 1: Update updated_at timestamp on profiles table
CREATE TRIGGER trigger_update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();

-- Add comments to triggers
COMMENT ON TRIGGER trigger_update_profiles_updated_at ON public.profiles IS 'Automatically updates updated_at timestamp when profile is modified';

-- =====================================================
-- ALTERNATIVE: Database Webhooks for User Creation
-- =====================================================
-- Since we cannot create triggers on auth.users, use Supabase webhooks instead:
--
-- 1. Go to Supabase Dashboard → Database → Webhooks
-- 2. Create a webhook for "Insert" on "auth.users" table
-- 3. Set the webhook URL to call your application endpoint
-- 4. In your application, call public.create_profile_for_user() function
--
-- Example webhook payload:
-- {
--   "type": "INSERT",
--   "table": "users",
--   "schema": "auth",
--   "record": {
--     "id": "uuid-here",
--     "email": "<EMAIL>",
--     "raw_user_meta_data": {...}
--   }
-- }

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.update_updated_at_column() TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_profile_for_user(UUID, TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_user_last_login(UUID) TO authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Database functions and triggers created successfully:';
    RAISE NOTICE '- update_updated_at_column(): Updates timestamps automatically';
    RAISE NOTICE '- create_profile_for_user(): Creates profiles for new users (call manually)';
    RAISE NOTICE '- update_user_last_login(): Tracks user login times (call from app)';
    RAISE NOTICE '';
    RAISE NOTICE 'NOTE: Auto profile creation requires webhook setup or manual calls';
    RAISE NOTICE 'See comments in this file for webhook configuration instructions';
END $$;
